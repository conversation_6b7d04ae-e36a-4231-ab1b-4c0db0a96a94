:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Color Variables */
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #4ade80;
  --warning-color: #fbbf24;
  --danger-color: #f87171;

  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --text-white: #ffffff;

  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  --border-color: #e5e7eb;
  --border-radius: 12px;
  --border-radius-lg: 20px;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-primary);
  overflow-x: hidden;
}

#app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Container Styles */
.container {
  width: 100%;
  max-width: 500px;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: 40px;
  text-align: center;
  position: relative;
}

.hidden {
  display: none !important;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.settings-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: background-color 0.2s ease;
}

.settings-btn:hover {
  background-color: var(--bg-secondary);
}

/* Timer Display Styles */
.timer-display {
  margin-bottom: 40px;
}

.session-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.session-type {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--primary-color);
}

.session-counter {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Timer Circle Styles */
.timer-circle {
  position: relative;
  display: inline-block;
  margin-bottom: 30px;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-background {
  fill: none;
  stroke: var(--bg-secondary);
  stroke-width: 8;
}

.progress-ring-fill {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 8;
  stroke-linecap: round;
  stroke-dasharray: 879.646; /* 2 * π * 140 */
  stroke-dashoffset: 879.646;
  transition: stroke-dashoffset 1s ease;
}

.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 3rem;
  font-weight: 300;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.timer-separator {
  margin: 0 8px;
  opacity: 0.7;
}

/* Session Dots */
.session-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 30px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
}

.dot.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.dot.completed {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.dot.long-break {
  width: 16px;
  height: 16px;
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

/* Control Buttons */
.timer-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
}

.control-btn {
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.control-btn.primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.control-btn.primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.control-btn.secondary {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.control-btn.secondary:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

.control-btn.large {
  padding: 16px 32px;
  font-size: 1.1rem;
}

.control-btn.small {
  padding: 8px 16px;
  font-size: 0.9rem;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Stats */
.stats {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Break Confirmation Styles */
.break-content {
  padding: 20px 0;
}

.break-title {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.break-message {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 30px;
  line-height: 1.6;
}

.break-info {
  margin-bottom: 40px;
}

.break-duration {
  margin-bottom: 16px;
}

.break-type {
  display: block;
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.break-time {
  display: block;
  font-size: 2rem;
  font-weight: 300;
  color: var(--text-primary);
}

.bonus-time {
  padding: 12px 20px;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--warning-color);
}

.bonus-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-right: 8px;
}

.bonus-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--warning-color);
}

.break-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* Settings Panel Styles */
.settings-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.settings-content {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  overflow-y: auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 30px 20px;
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.settings-sections {
  padding: 30px;
}

.settings-section {
  margin-bottom: 40px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--primary-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
  flex: 1;
}

.setting-item input,
.setting-item select {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 8px 12px;
  font-size: 1rem;
  font-family: inherit;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.setting-item input:focus,
.setting-item select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-item input[type="number"] {
  width: 80px;
}

.setting-item input[type="range"] {
  width: 120px;
}

.setting-item input[type="color"] {
  width: 50px;
  height: 40px;
  padding: 2px;
  border-radius: var(--border-radius);
}

.setting-item input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: var(--primary-color);
}

.setting-item select {
  min-width: 150px;
}

#volume-display {
  font-size: 0.9rem;
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    max-width: 100%;
    padding: 30px 20px;
    margin: 10px;
  }

  .timer-text {
    font-size: 2.5rem;
  }

  .timer-controls {
    flex-direction: column;
    gap: 12px;
  }

  .control-btn {
    width: 100%;
  }

  .stats {
    flex-direction: column;
    gap: 16px;
  }

  .settings-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .settings-header,
  .settings-sections {
    padding: 20px;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .setting-item input,
  .setting-item select {
    width: 100%;
  }

  .setting-item input[type="number"] {
    width: 100%;
  }

  .setting-item input[type="range"] {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.2rem;
  }

  .timer-text {
    font-size: 2rem;
  }

  .session-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .break-title {
    font-size: 1.5rem;
  }

  .break-controls {
    flex-direction: column;
  }
}

/* Custom Background Styles */
.custom-background {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.solid-background {
  background: var(--custom-bg-color, var(--primary-color)) !important;
}

/* Animation Classes */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 1s ease-in-out infinite;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.shake {
  animation: shake 0.5s ease-in-out;
}
