<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pomodoro Timer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="app">
      <!-- Main Timer Interface -->
      <div id="timer-container" class="container">
        <header class="header">
          <h1 class="app-title">Pomodoro Timer</h1>
          <button id="settings-btn" class="settings-btn">⚙️</button>
        </header>

        <main class="main-content">
          <!-- Timer Display -->
          <div id="timer-display" class="timer-display">
            <div class="session-info">
              <span id="session-type" class="session-type">Focus Time</span>
              <span id="session-counter" class="session-counter">Session 1</span>
            </div>

            <div class="timer-circle">
              <svg class="progress-ring" width="300" height="300">
                <defs>
                  <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:var(--primary-color);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:var(--secondary-color);stop-opacity:1" />
                  </linearGradient>
                </defs>
                <circle class="progress-ring-background" cx="150" cy="150" r="140" />
                <circle id="progress-ring-fill" class="progress-ring-fill" cx="150" cy="150" r="140" />
              </svg>
              <div class="timer-text">
                <span id="timer-minutes" class="timer-minutes">25</span>
                <span class="timer-separator">:</span>
                <span id="timer-seconds" class="timer-seconds">00</span>
              </div>
            </div>

            <!-- Session Progress Dots -->
            <div id="session-dots" class="session-dots">
              <div class="dot active"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot long-break"></div>
            </div>
          </div>

          <!-- Timer Controls -->
          <div class="timer-controls">
            <button id="start-pause-btn" class="control-btn primary">Start</button>
            <button id="reset-btn" class="control-btn secondary">Reset</button>
            <button id="skip-btn" class="control-btn secondary">Skip</button>
          </div>

          <!-- Stats -->
          <div class="stats">
            <div class="stat-item">
              <span class="stat-label">Completed Sessions</span>
              <span id="completed-sessions" class="stat-value">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Total Focus Time</span>
              <span id="total-focus-time" class="stat-value">0h 0m</span>
            </div>
          </div>
        </main>
      </div>

      <!-- Break Confirmation Screen -->
      <div id="break-confirmation" class="container hidden">
        <div class="break-content">
          <h2 id="break-title" class="break-title">Time for a Break!</h2>
          <p id="break-message" class="break-message">You've completed a focus session. Ready to start your break?</p>

          <div class="break-info">
            <div class="break-duration">
              <span id="break-type" class="break-type">Short Break</span>
              <span id="break-time" class="break-time">5 minutes</span>
            </div>
            <div id="bonus-time" class="bonus-time hidden">
              <span class="bonus-label">Bonus Time:</span>
              <span id="bonus-minutes" class="bonus-value">+2 minutes</span>
            </div>
          </div>

          <div class="break-controls">
            <button id="start-break-btn" class="control-btn primary large">Start Break</button>
            <button id="skip-break-btn" class="control-btn secondary">Skip Break</button>
          </div>
        </div>
      </div>

      <!-- Settings Panel -->
      <div id="settings-panel" class="settings-panel hidden">
        <div class="settings-content">
          <header class="settings-header">
            <h2>Settings</h2>
            <button id="close-settings-btn" class="close-btn">×</button>
          </header>

          <div class="settings-sections">
            <!-- Timer Settings -->
            <section class="settings-section">
              <h3>Timer Settings</h3>
              <div class="setting-item">
                <label for="work-duration">Work Session (minutes)</label>
                <input type="number" id="work-duration" min="1" max="60" value="25">
              </div>
              <div class="setting-item">
                <label for="short-break-duration">Short Break (minutes)</label>
                <input type="number" id="short-break-duration" min="1" max="30" value="5">
              </div>
              <div class="setting-item">
                <label for="long-break-duration">Long Break (minutes)</label>
                <input type="number" id="long-break-duration" min="1" max="60" value="15">
              </div>
            </section>

            <!-- Music Settings -->
            <section class="settings-section">
              <h3>Music Settings</h3>
              <div class="setting-item horizontal">
                <label for="music-enabled">Enable Focus Music</label>
                <input type="checkbox" id="music-enabled" checked>
              </div>
              <div class="setting-item volume-control">
                <label for="music-volume">Volume</label>
                <input type="range" id="music-volume" min="0" max="100" value="50">
                <span id="volume-display">50%</span>
              </div>
              <div class="setting-item track-selection">
                <label for="music-track">Select Track</label>
                <div style="display: flex; gap: var(--spacing-sm);">
                  <select id="music-track">
                    <option value="">No music files found</option>
                  </select>
                  <button id="load-music-btn" class="control-btn secondary small">Load Music Folder</button>
                </div>
              </div>
            </section>

            <!-- Appearance Settings -->
            <section class="settings-section">
              <h3>Appearance</h3>
              <div class="setting-item">
                <label for="background-type">Background</label>
                <select id="background-type">
                  <option value="gradient">Gradient</option>
                  <option value="solid">Solid Color</option>
                  <option value="image">Custom Image</option>
                </select>
              </div>
              <div class="setting-item">
                <label for="background-color">Background Color</label>
                <input type="color" id="background-color" value="#667eea">
              </div>
              <div class="setting-item hidden" id="background-image-setting">
                <label for="background-image">Background Image</label>
                <input type="file" id="background-image" accept="image/*">
              </div>
            </section>
          </div>
        </div>
      </div>

      <!-- Music Player (Hidden) -->
      <audio id="music-player" loop></audio>

      <!-- File Input for Music (Hidden) -->
      <input type="file" id="music-file-input" multiple accept="audio/mp3" style="display: none;">
    </div>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
