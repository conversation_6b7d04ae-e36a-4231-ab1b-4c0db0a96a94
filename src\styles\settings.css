/* Settings Panel Styles */
.settings-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.settings-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp var(--transition-normal) ease-out;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--bg-primary);
  z-index: 10;
}

.settings-header h2 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  color: var(--text-secondary);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.settings-body {
  padding: var(--spacing-xl);
}

.settings-section {
  margin-bottom: var(--spacing-2xl);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input[type="range"] {
  height: 6px;
  background: var(--border-color);
  border: none;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.form-input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.form-input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.checkbox-input {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.checkbox-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  cursor: pointer;
  margin: 0;
  text-transform: none;
  letter-spacing: normal;
}

.color-picker-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.color-option {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius);
  border: 3px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.color-option.selected {
  border-color: var(--primary-color);
  transform: scale(1.1);
}

.color-option:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.file-input-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
  width: 100%;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-label {
  display: block;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  text-align: center;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.file-input-label:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.music-track-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-sm);
}

.music-track-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  transition: background var(--transition-fast);
}

.music-track-item:last-child {
  border-bottom: none;
}

.music-track-item:hover {
  background: var(--bg-secondary);
}

.music-track-item.selected {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary-color);
}

.track-name {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  cursor: pointer;
}

.track-duration {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.settings-actions {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border-top: 2px solid var(--border-color);
  background: var(--bg-secondary);
}

.settings-btn {
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 100px;
}

.settings-btn.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.settings-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.settings-btn.secondary {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.settings-btn.secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}
