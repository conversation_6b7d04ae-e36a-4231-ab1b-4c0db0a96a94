/**
 * TimerView - <PERSON>les timer UI display and user interactions
 * Follows MVP pattern - only handles DOM manipulation and UI events
 */
export class TimerView {
  constructor() {
    // DOM elements
    this.elements = {
      container: null,
      sessionType: null,
      sessionCounter: null,
      timerMinutes: null,
      timerSeconds: null,
      progressRing: null,
      sessionDots: null,
      startPauseBtn: null,
      resetBtn: null,
      skipBtn: null,
      completedSessions: null,
      totalFocusTime: null,
      settingsBtn: null
    }
    
    // Event callbacks (set by presenter)
    this.callbacks = {}
    
    // Progress ring properties
    this.progressRingRadius = 140
    this.progressRingCircumference = 2 * Math.PI * this.progressRingRadius
    
    this.initializeElements()
    this.setupEventListeners()
  }
  
  /**
   * Initialize DOM elements
   */
  initializeElements() {
    console.log('TimerView: Initializing DOM elements...')

    this.elements.container = document.getElementById('timer-container')
    this.elements.sessionType = document.getElementById('session-type')
    this.elements.sessionCounter = document.getElementById('session-counter')
    this.elements.timerMinutes = document.getElementById('timer-minutes')
    this.elements.timerSeconds = document.getElementById('timer-seconds')
    this.elements.progressRing = document.getElementById('progress-ring-fill')
    this.elements.sessionDots = document.getElementById('session-dots')
    this.elements.startPauseBtn = document.getElementById('start-pause-btn')
    this.elements.resetBtn = document.getElementById('reset-btn')
    this.elements.skipBtn = document.getElementById('skip-btn')
    this.elements.completedSessions = document.getElementById('completed-sessions')
    this.elements.totalFocusTime = document.getElementById('total-focus-time')
    this.elements.settingsBtn = document.getElementById('settings-btn')

    // Log which elements were found
    Object.entries(this.elements).forEach(([key, element]) => {
      if (!element) {
        console.warn(`TimerView: Element '${key}' not found`)
      }
    })

    // Initialize progress ring
    if (this.elements.progressRing) {
      this.elements.progressRing.style.strokeDasharray = this.progressRingCircumference
      // Start with full circle hidden for work sessions (empty to full)
      this.elements.progressRing.style.strokeDashoffset = this.progressRingCircumference
    }

    // Track current session type for progress direction
    this.currentSessionType = 'work'

    console.log('TimerView: DOM elements initialized')
  }
  
  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Timer control buttons
    if (this.elements.startPauseBtn) {
      this.elements.startPauseBtn.addEventListener('click', () => {
        this.callbacks.onStartPause?.()
      })
    }
    
    if (this.elements.resetBtn) {
      this.elements.resetBtn.addEventListener('click', () => {
        this.callbacks.onReset?.()
      })
    }
    
    if (this.elements.skipBtn) {
      this.elements.skipBtn.addEventListener('click', () => {
        this.callbacks.onSkip?.()
      })
    }
    
    if (this.elements.settingsBtn) {
      this.elements.settingsBtn.addEventListener('click', () => {
        this.callbacks.onSettings?.()
      })
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Only handle shortcuts when timer container is visible
      if (!this.isVisible()) return
      
      switch (e.code) {
        case 'Space':
          e.preventDefault()
          this.callbacks.onStartPause?.()
          break
        case 'KeyR':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            this.callbacks.onReset?.()
          }
          break
        case 'KeyS':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            this.callbacks.onSkip?.()
          }
          break
        case 'Escape':
          this.callbacks.onSettings?.()
          break
      }
    })
  }
  
  /**
   * Set event callbacks
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }
  
  /**
   * Update timer display
   */
  updateTimer(minutes, seconds) {
    if (this.elements.timerMinutes) {
      this.elements.timerMinutes.textContent = minutes
    }
    if (this.elements.timerSeconds) {
      this.elements.timerSeconds.textContent = seconds
    }
  }
  
  /**
   * Update progress ring
   */
  updateProgress(progress, sessionType = null) {
    if (!this.elements.progressRing) return

    // Update session type if provided
    if (sessionType) {
      this.currentSessionType = sessionType
    }

    // Progress is already adjusted in presenter:
    // - Work sessions: 0 = start (empty), 1 = end (full)
    // - Break sessions: 1 = start (full), 0 = end (empty)
    // So we always use the same formula: progress 0 = hidden, progress 1 = shown
    const offset = this.progressRingCircumference * (1 - progress)

    this.elements.progressRing.style.strokeDashoffset = offset
  }
  
  /**
   * Update session information
   */
  updateSessionInfo(sessionType, sessionNumber) {
    // Update current session type for progress direction
    const previousSessionType = this.currentSessionType
    this.currentSessionType = sessionType

    if (this.elements.sessionType) {
      const sessionNames = {
        work: 'Focus Time',
        shortBreak: 'Short Break',
        longBreak: 'Long Break'
      }
      this.elements.sessionType.textContent = sessionNames[sessionType] || sessionType
    }

    if (this.elements.sessionCounter) {
      this.elements.sessionCounter.textContent = `Session ${sessionNumber}`
    }

    // Update progress ring color based on session type
    if (this.elements.progressRing) {
      this.elements.progressRing.classList.remove('work', 'short-break', 'long-break')
      this.elements.progressRing.classList.add(sessionType.replace(/([A-Z])/g, '-$1').toLowerCase())

      // Reset progress ring position when session type changes
      if (previousSessionType !== sessionType) {
        if (sessionType === 'work') {
          // Work sessions start empty (full offset = hidden)
          this.elements.progressRing.style.strokeDashoffset = this.progressRingCircumference
        } else {
          // Break sessions start full (zero offset = shown)
          this.elements.progressRing.style.strokeDashoffset = '0'
        }
      }
    }
  }
  
  /**
   * Update session dots
   */
  updateSessionDots(currentSession, completedSessions, sessionsUntilLongBreak) {
    if (!this.elements.sessionDots) return
    
    // Clear existing dots
    this.elements.sessionDots.innerHTML = ''
    
    // Create dots for sessions until long break
    for (let i = 1; i <= sessionsUntilLongBreak; i++) {
      const dot = document.createElement('div')
      dot.className = 'dot'
      
      if (i <= completedSessions) {
        dot.classList.add('completed')
      } else if (i === currentSession && currentSession <= sessionsUntilLongBreak) {
        dot.classList.add('active')
      }
      
      this.elements.sessionDots.appendChild(dot)
    }
    
    // Add long break dot
    const longBreakDot = document.createElement('div')
    longBreakDot.className = 'dot long-break'
    
    if (completedSessions >= sessionsUntilLongBreak) {
      longBreakDot.classList.add('completed')
    } else if (currentSession > sessionsUntilLongBreak) {
      longBreakDot.classList.add('active')
    }
    
    this.elements.sessionDots.appendChild(longBreakDot)
  }
  
  /**
   * Update start/pause button
   */
  updateStartPauseButton(isRunning, isPaused) {
    if (!this.elements.startPauseBtn) return
    
    if (isRunning && !isPaused) {
      this.elements.startPauseBtn.textContent = 'Pause'
      this.elements.startPauseBtn.classList.remove('primary')
      this.elements.startPauseBtn.classList.add('secondary')
    } else {
      this.elements.startPauseBtn.textContent = isPaused ? 'Resume' : 'Start'
      this.elements.startPauseBtn.classList.remove('secondary')
      this.elements.startPauseBtn.classList.add('primary')
    }
  }
  
  /**
   * Update statistics
   */
  updateStats(completedSessions, totalFocusTime) {
    if (this.elements.completedSessions) {
      this.elements.completedSessions.textContent = completedSessions
    }
    
    if (this.elements.totalFocusTime) {
      this.elements.totalFocusTime.textContent = totalFocusTime
    }
  }
  
  /**
   * Show timer container
   */
  show() {
    if (this.elements.container) {
      this.elements.container.classList.remove('hidden')
    }
  }
  
  /**
   * Hide timer container
   */
  hide() {
    if (this.elements.container) {
      this.elements.container.classList.add('hidden')
    }
  }
  
  /**
   * Check if timer container is visible
   */
  isVisible() {
    return this.elements.container && !this.elements.container.classList.contains('hidden')
  }
  
  /**
   * Add visual feedback for timer state
   */
  setTimerState(state) {
    if (!this.elements.container) return
    
    // Remove all state classes
    this.elements.container.classList.remove('running', 'paused', 'completed')
    
    // Add current state class
    if (state) {
      this.elements.container.classList.add(state)
    }
  }
  
  /**
   * Animate timer completion
   */
  animateCompletion() {
    if (!this.elements.progressRing) return
    
    // Add completion animation class
    this.elements.progressRing.classList.add('completed')
    
    // Remove after animation
    setTimeout(() => {
      this.elements.progressRing.classList.remove('completed')
    }, 1000)
  }
  
  /**
   * Show notification or visual feedback
   */
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = `notification ${type}`
    notification.textContent = message
    
    // Add to container
    if (this.elements.container) {
      this.elements.container.appendChild(notification)
      
      // Animate in
      setTimeout(() => notification.classList.add('show'), 10)
      
      // Remove after delay
      setTimeout(() => {
        notification.classList.remove('show')
        setTimeout(() => notification.remove(), 300)
      }, 3000)
    }
  }
  
  /**
   * Enable/disable controls
   */
  setControlsEnabled(enabled) {
    const controls = [
      this.elements.startPauseBtn,
      this.elements.resetBtn,
      this.elements.skipBtn
    ]
    
    controls.forEach(control => {
      if (control) {
        control.disabled = !enabled
      }
    })
  }
  
  /**
   * Add pulse animation to timer
   */
  addPulseAnimation() {
    if (this.elements.progressRing) {
      this.elements.progressRing.classList.add('pulse')
    }
  }
  
  /**
   * Remove pulse animation from timer
   */
  removePulseAnimation() {
    if (this.elements.progressRing) {
      this.elements.progressRing.classList.remove('pulse')
    }
  }
  
  /**
   * Get DOM element for external access
   */
  getElement(name) {
    return this.elements[name]
  }
}
