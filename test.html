<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pomodoro</title>
</head>
<body>
    <h1>Testing Pomodoro App</h1>
    <div id="test-output"></div>
    
    <script type="module">
        import { TimerModel } from './src/models/TimerModel.js'
        import { EventEmitter } from './src/utils/EventEmitter.js'
        
        const output = document.getElementById('test-output')
        
        function log(message) {
            const p = document.createElement('p')
            p.textContent = message
            output.appendChild(p)
            console.log(message)
        }
        
        try {
            log('Testing EventEmitter...')
            const emitter = new EventEmitter()
            emitter.on('test', (data) => log(`Event received: ${data}`))
            emitter.emit('test', 'Hello World!')
            log('✓ EventEmitter works')
            
            log('Testing TimerModel...')
            const timer = new TimerModel()
            log(`✓ TimerModel created with state: ${JSON.stringify(timer.getState())}`)
            
            log('All tests passed!')
        } catch (error) {
            log(`❌ Error: ${error.message}`)
            console.error(error)
        }
    </script>
</body>
</html>
