// Test file to check if all imports work correctly
import { TimerModel } from './src/models/TimerModel.js'
import { MusicModel } from './src/models/MusicModel.js'
import { SettingsModel } from './src/models/SettingsModel.js'
import { TimerView } from './src/views/TimerView.js'
import { BreakConfirmationView } from './src/views/BreakConfirmationView.js'
import { MusicPlayerView } from './src/views/MusicPlayerView.js'
import { SettingsView } from './src/views/SettingsView.js'
import { TimerPresenter } from './src/presenters/TimerPresenter.js'
import { MusicPresenter } from './src/presenters/MusicPresenter.js'
import { SettingsPresenter } from './src/presenters/SettingsPresenter.js'
import { EventEmitter } from './src/utils/EventEmitter.js'

console.log('All imports successful!')

// Test basic instantiation
try {
  const eventEmitter = new EventEmitter()
  console.log('✓ EventEmitter created')
  
  const timerModel = new TimerModel()
  console.log('✓ TimerModel created')
  
  const musicModel = new MusicModel()
  console.log('✓ MusicModel created')
  
  const settingsModel = new SettingsModel()
  console.log('✓ SettingsModel created')
  
  console.log('All models instantiated successfully!')
} catch (error) {
  console.error('Error creating models:', error)
}
