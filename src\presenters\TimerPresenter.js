/**
 * TimerPresenter - Connects TimerModel with TimerView and BreakConfirmationView
 * Follows MVP pattern - handles business logic and coordinates between model and views
 */
import { TimerModel } from '../models/TimerModel.js'
import { TimerView } from '../views/TimerView.js'
import { BreakConfirmationView } from '../views/BreakConfirmationView.js'
import { EventEmitter } from '../utils/EventEmitter.js'

export class TimerPresenter extends EventEmitter {
  constructor() {
    super()
    
    // Initialize model and views
    this.model = new TimerModel()
    this.timerView = new TimerView()
    this.breakView = new BreakConfirmationView()
    
    // Current state
    this.currentView = 'timer' // 'timer' or 'break'
    
    this.setupModelListeners()
    this.setupViewCallbacks()
    this.initializeUI()
  }
  
  /**
   * Initialize the presenter
   */
  init() {
    console.log('TimerPresenter: Initializing...')
    this.updateUI()
    this.timerView.show()
    console.log('TimerPresenter: Initialized and timer view shown')
  }
  
  /**
   * Setup model event listeners
   */
  setupModelListeners() {
    // Timer events
    this.model.on('timerStarted', (data) => {
      this.timerView.updateStartPauseButton(true, false)
      this.timerView.setTimerState('running')
      this.emit('sessionStart', data.sessionType)
    })
    
    this.model.on('timerPaused', () => {
      this.timerView.updateStartPauseButton(true, true)
      this.timerView.setTimerState('paused')
      this.emit('timerPause')
    })
    
    this.model.on('timerResumed', () => {
      this.timerView.updateStartPauseButton(true, false)
      this.timerView.setTimerState('running')
      this.emit('timerResume')
    })
    
    this.model.on('timerReset', () => {
      this.timerView.updateStartPauseButton(false, false)
      this.timerView.setTimerState('')
      this.updateUI()
    })
    
    this.model.on('timerTick', (data) => {
      this.updateTimerDisplay(data.timeRemaining)
      this.timerView.updateProgress(data.progress, this.model.getState().currentSession)
      // Update stats in real-time if totalFocusTime is provided
      if (data.totalFocusTime !== undefined) {
        this.timerView.updateStats(
          this.model.getState().completedSessions,
          this.model.formatTotalTime(data.totalFocusTime)
        )
      }
    })
    
    // Session events
    this.model.on('sessionCompleted', (data) => {
      this.timerView.animateCompletion()
      this.timerView.updateStats(data.completedSessions, this.model.formatTotalTime(data.totalFocusTime))
      this.emit('sessionEnd', data.sessionType)
    })
    
    this.model.on('sessionSkipped', (data) => {
      this.updateUI()
      this.timerView.showNotification(`Skipped to ${this.getSessionDisplayName(data.sessionType)}`, 'info')
    })
    
    // Break confirmation events
    this.model.on('breakConfirmationStarted', (data) => {
      this.showBreakConfirmation(data.nextBreakType, data.breakDuration)
    })
    
    this.model.on('bonusTimeUpdated', (data) => {
      this.breakView.updateBonusTime(data.bonusTime)
    })
    
    this.model.on('breakConfirmed', (data) => {
      this.hideBreakConfirmation()
      this.updateUI()
      
      if (data.bonusTime > 0) {
        const bonusMinutes = Math.floor(data.bonusTime / 60)
        const bonusSeconds = data.bonusTime % 60
        let message = `Break started with +${bonusMinutes}m`
        if (bonusSeconds > 0) message += ` ${bonusSeconds}s`
        message += ' bonus time!'
        this.timerView.showNotification(message, 'success')
      }
    })
    
    this.model.on('breakSkipped', (data) => {
      this.hideBreakConfirmation()
      this.updateUI()
      this.timerView.showNotification('Break skipped, back to work!', 'info')
    })
    
    this.model.on('breakEnded', (data) => {
      this.updateUI()
      this.timerView.showNotification(`Break ended! Starting ${this.getSessionDisplayName(data.nextSessionType)}`, 'success')
    })
  }
  
  /**
   * Setup view callbacks
   */
  setupViewCallbacks() {
    // Timer view callbacks
    this.timerView.setCallbacks({
      onStartPause: () => this.handleStartPause(),
      onReset: () => this.handleReset(),
      onSkip: () => this.handleSkip(),
      onSettings: () => this.handleSettings()
    })
    
    // Break confirmation view callbacks
    this.breakView.setCallbacks({
      onStartBreak: () => this.handleStartBreak(),
      onSkipBreak: () => this.handleSkipBreak()
    })
  }
  
  /**
   * Initialize UI with current state
   */
  initializeUI() {
    this.updateUI()
  }
  
  /**
   * Update entire UI based on current model state
   */
  updateUI() {
    const state = this.model.getState()
    const config = this.model.getConfig()
    
    // Update timer display
    this.updateTimerDisplay(state.timeRemaining)
    
    // Update session info
    this.timerView.updateSessionInfo(state.currentSession, state.sessionNumber)
    
    // Update session dots
    this.timerView.updateSessionDots(
      state.sessionNumber,
      state.completedSessions,
      config.sessionsUntilLongBreak
    )
    
    // Update controls
    this.timerView.updateStartPauseButton(state.isRunning, state.isPaused)
    
    // Update stats
    this.timerView.updateStats(
      state.completedSessions,
      this.model.formatTotalTime(state.totalFocusTime)
    )
    
    // Update progress
    const progress = state.totalTime > 0 ? (state.totalTime - state.timeRemaining) / state.totalTime : 0
    this.timerView.updateProgress(progress, state.currentSession)
  }
  
  /**
   * Update timer display
   */
  updateTimerDisplay(timeRemaining) {
    const formatted = this.model.formatTime(timeRemaining)
    this.timerView.updateTimer(formatted.minutes, formatted.seconds)
  }
  
  /**
   * Handle start/pause button click
   */
  handleStartPause() {
    const state = this.model.getState()
    
    if (!state.isRunning) {
      this.model.start()
    } else if (state.isPaused) {
      this.model.resume()
    } else {
      this.model.pause()
    }
  }
  
  /**
   * Handle reset button click
   */
  handleReset() {
    this.model.reset()
  }
  
  /**
   * Handle skip button click
   */
  handleSkip() {
    this.model.skip()
  }
  
  /**
   * Handle settings button click
   */
  handleSettings() {
    this.emit('settingsRequested')
  }
  
  /**
   * Handle start break button click
   */
  handleStartBreak() {
    this.model.confirmBreak()
  }
  
  /**
   * Handle skip break button click
   */
  handleSkipBreak() {
    this.model.skipBreak()
  }
  
  /**
   * Show break confirmation
   */
  showBreakConfirmation(breakType, breakDuration) {
    this.currentView = 'break'
    this.timerView.hide()
    
    const state = this.model.getState()
    this.breakView.show(breakType, breakDuration, state.sessionNumber)
  }
  
  /**
   * Hide break confirmation
   */
  hideBreakConfirmation() {
    this.currentView = 'timer'
    this.breakView.hide()
    this.timerView.show()
  }
  
  /**
   * Update settings from external source
   */
  updateSettings(settings) {
    if (settings.timer) {
      this.model.updateConfig(settings.timer)
    }
  }
  
  /**
   * Get session display name
   */
  getSessionDisplayName(sessionType) {
    const names = {
      work: 'Focus Time',
      shortBreak: 'Short Break',
      longBreak: 'Long Break'
    }
    return names[sessionType] || sessionType
  }

  /**
   * Get current stats for reporting
   */
  getStats() {
    const state = this.model.getState()
    return {
      focusTime: this.model.formatTotalTime(state.totalFocusTime),
      completedSessions: state.completedSessions
    }
  }
  
  /**
   * Get current timer state for external access
   */
  getCurrentState() {
    return {
      ...this.model.getState(),
      currentView: this.currentView,
      breakState: this.model.getBreakState()
    }
  }
  
  /**
   * Force update UI (useful for external changes)
   */
  forceUpdate() {
    this.updateUI()
  }
  
  /**
   * Check if timer is currently running
   */
  isRunning() {
    return this.model.getState().isRunning
  }
  
  /**
   * Check if in break confirmation mode
   */
  isInBreakConfirmation() {
    return this.currentView === 'break'
  }

  /**
   * Check if timer is running
   */
  isRunning() {
    return this.model.getState().isRunning
  }
  

}
