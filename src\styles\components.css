/* Component Styles */

/* Container Styles */
.container {
  width: 100%;
  max-width: var(--container-lg); /* Increased from 500px to 800px */
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-2xl); /* Increased padding */
  text-align: center;
  position: relative;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
}

.header-buttons {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.settings-btn,
.report-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  color: var(--primary-color);
  width: 48px;
  height: 48px;
  min-width: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  aspect-ratio: 1;
}

.settings-btn svg,
.report-btn svg {
  width: 24px;
  height: 24px;
  transition: all var(--transition-fast);
}

.settings-btn:hover {
  background: var(--bg-secondary);
}

.settings-btn:hover svg {
  transform: rotate(90deg);
}

.settings-btn:active {
  transform: scale(0.95);
}

.settings-btn:active svg {
  transform: rotate(90deg) scale(0.95);
}

.report-btn:hover {
  background: var(--bg-secondary);
}

.report-btn:hover svg {
  transform: scale(1.1);
}

.report-btn:active {
  transform: scale(0.95);
}

/* Main Content */
.main-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* Timer Display */
.timer-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.session-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.session-type {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.session-counter {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  font-weight: 500;
}

/* Timer Circle */
.timer-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--spacing-xl) 0;
}

.progress-ring {
  transform: rotate(-90deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.progress-ring-background {
  fill: none;
  stroke: var(--border-color);
  stroke-width: 8;
}

.progress-ring-fill {
  fill: none;
  stroke: url(#progressGradient);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dashoffset var(--transition-normal) ease-in-out;
  /* JavaScript will set stroke-dasharray and stroke-dashoffset dynamically */
}

.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.timer-minutes,
.timer-seconds {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  line-height: 1;
}

.timer-separator {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-secondary);
  animation: pulse 1s infinite;
}

/* Session Progress Dots */
.session-dots {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}

.session-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
}

.session-dot.completed {
  background: var(--success-color);
  transform: scale(1.2);
}

.session-dot.current {
  background: var(--primary-color);
  transform: scale(1.3);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.3);
}

/* Timer Controls */
.timer-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin: var(--spacing-xl) 0;
}

.control-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.control-btn.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-white);
  box-shadow: var(--shadow-md);
}

.control-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.control-btn.primary:active {
  transform: translateY(0);
}

.control-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
}

.control-btn.secondary:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Stats */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 2px solid var(--border-color);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  transition: all var(--transition-normal);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  font-variant-numeric: tabular-nums;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-overlay);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.report-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.report-stats .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.report-stats .stat-label {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
}

.report-stats .stat-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
}
