<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Pomodoro Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .timer-display {
            text-align: center;
            margin: 20px 0;
        }
        .timer-text {
            font-size: 4rem;
            font-weight: bold;
            margin: 20px 0;
        }
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            background: #667eea;
            color: white;
        }
        button:hover {
            background: #5a6fd8;
        }
        .log {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Pomodoro Test</h1>
        
        <div class="timer-display">
            <div id="session-type">Focus Time</div>
            <div class="timer-text">
                <span id="timer-minutes">25</span>:<span id="timer-seconds">00</span>
            </div>
        </div>
        
        <div class="controls">
            <button id="start-pause-btn">Start</button>
            <button id="reset-btn">Reset</button>
            <button id="skip-btn">Skip</button>
        </div>
        
        <div id="log-output"></div>
    </div>
    
    <script type="module">
        const logOutput = document.getElementById('log-output')
        
        function log(message) {
            const div = document.createElement('div')
            div.className = 'log'
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`
            logOutput.appendChild(div)
            console.log(message)
        }
        
        try {
            log('Importing TimerPresenter...')
            const { TimerPresenter } = await import('./src/presenters/TimerPresenter.js')
            
            log('Creating TimerPresenter...')
            const timerPresenter = new TimerPresenter()
            
            log('Initializing TimerPresenter...')
            timerPresenter.init()
            
            log('✓ Timer initialized successfully!')
            
            // Test button clicks
            document.getElementById('start-pause-btn').addEventListener('click', () => {
                log('Start/Pause button clicked')
            })
            
        } catch (error) {
            log(`❌ Error: ${error.message}`)
            console.error(error)
        }
    </script>
</body>
</html>
