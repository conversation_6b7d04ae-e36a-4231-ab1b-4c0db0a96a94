<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Pomodoro</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>Pomodoro App Debug</h1>
    <div id="output"></div>
    
    <script type="module">
        const output = document.getElementById('output')
        
        function log(message, type = 'log') {
            const div = document.createElement('div')
            div.className = `log ${type}`
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`
            output.appendChild(div)
            console.log(message)
        }
        
        try {
            log('Starting debug...', 'success')
            
            // Test EventEmitter
            log('Testing EventEmitter...')
            const { EventEmitter } = await import('./src/utils/EventEmitter.js')
            const emitter = new EventEmitter()
            log('✓ EventEmitter imported and created', 'success')
            
            // Test Models
            log('Testing Models...')
            const { TimerModel } = await import('./src/models/TimerModel.js')
            const { MusicModel } = await import('./src/models/MusicModel.js')
            const { SettingsModel } = await import('./src/models/SettingsModel.js')
            
            const timerModel = new TimerModel()
            const musicModel = new MusicModel()
            const settingsModel = new SettingsModel()
            
            log('✓ All models created successfully', 'success')
            log(`Timer state: ${JSON.stringify(timerModel.getState())}`)
            
            // Test Views
            log('Testing Views...')
            const { TimerView } = await import('./src/views/TimerView.js')
            const { BreakConfirmationView } = await import('./src/views/BreakConfirmationView.js')
            const { MusicPlayerView } = await import('./src/views/MusicPlayerView.js')
            const { SettingsView } = await import('./src/views/SettingsView.js')
            
            log('✓ All views imported successfully', 'success')
            
            // Test Presenters
            log('Testing Presenters...')
            const { TimerPresenter } = await import('./src/presenters/TimerPresenter.js')
            const { MusicPresenter } = await import('./src/presenters/MusicPresenter.js')
            const { SettingsPresenter } = await import('./src/presenters/SettingsPresenter.js')
            
            log('✓ All presenters imported successfully', 'success')
            
            log('All imports successful! 🎉', 'success')
            
        } catch (error) {
            log(`❌ Error: ${error.message}`, 'error')
            log(`Stack: ${error.stack}`, 'error')
        }
    </script>
</body>
</html>
